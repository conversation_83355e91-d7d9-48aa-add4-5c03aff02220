<?php
/**
* Template Name: Oferta wynajmu
 */

get_header(); ?>

	

    <div class="mu-page-header" style="background-image: url('/wp-content/uploads/2021/09/header_ofertawynajmu.png');">
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <p class="mu-page-header--pagename">Muchowiecka apartments</p>
                    <h1>Oferta wynajmu</h1>
                </div>
            </div>
        </div>
    </div>

    <?php
        $args = array(
            'post_type' => 'oferta_wynajmu',
            'post_status' => 'publish',
            'posts_per_page' => 999,
            'ignore_sticky_posts' => 1,
            'orderby' => 'publish_date',
            'order' => 'DESC'
        );

        $post_query = new WP_Query($args);
        if($post_query->have_posts() ) {
            while($post_query->have_posts() ) {
                $post_query->the_post();
                ?>
                    <div class="mu-offer-item">
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mu-offer-item--thumbnail" style="background-image:url('<?php echo get_the_post_thumbnail_url(get_the_ID(),'large')?>');"></div>
                                </div>
                                <?php if( have_rows('sekcja_-_ogolne') ): 
                                    while( have_rows('sekcja_-_ogolne') ): the_row(); 
                                        $typ_lokalu = get_sub_field('typ_lokalu'); 
                                        $zajawka = get_sub_field('zajawka'); 
                                        $metraz = get_sub_field('metraz'); 
                                        $cena = get_sub_field('cena'); 
                                        $dzielnica = get_sub_field('dzielnica'); 
                                        $ulica = get_sub_field('ulica'); ?>
                                        <div class="col-lg-6">
                                            <article class="mu-offer-item--content">
                                                <header>
                                                <h2 class="mu-offer-item-content--title"><?php the_title(); ?></h2>
                                                <?php if($typ_lokalu): ?>
                                                    <p class="mu-offer-item-content--categories"><?php echo $typ_lokalu; ?></p>
                                                <?php endif; ?>
                                                <?php if($zajawka): ?>
                                                    <p class="mu-offer-item-content--desc"><?php echo $zajawka; ?></p>
                                                <?php endif; ?>
                                                </header>
                                                <section>
                                                    <ul class="mu-offer-item-content--params">
                                                        <?php if ($metraz): ?>
                                                            <li class="mu-offer-item-content-params--size">
                                                                <span class="sr-only">Powierzchnia</span>
                                                                <span aria-label="Metraż"><?php echo $metraz; ?> m<sup>2</sup></span>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if ($cena): ?>
                                                            <li class="mu-offer-item-content-params--price">
                                                                <span class="sr-only">Cena</span>
                                                                <span aria-label="Cena"><?php echo $cena; ?></span>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if ($dzielnica): ?>
                                                            <li class="mu-offer-item-content-params--zone">
                                                                <span class="sr-only">Dzielnica</span>
                                                                <span aria-label="Dzielnica"><?php echo $dzielnica; ?></span>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if ($ulica): ?>
                                                            <li class="mu-offer-item-content-params--street">
                                                                <span class="sr-only">Ulica</span>
                                                                <span aria-label="Ulica"><?php echo $ulica; ?></span>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </section>
                                                <a class="mu-primary-btn mu-primary-btn-color-main" href="<?php the_permalink(); ?>">Zobacz ofertę <i class="fas fa-long-arrow-alt-right"></i></a>
                                            </article>
                                        </div>
                                <?php endwhile;
                                endif; ?>
                            </div>
                        </div>
                    </div>
                <?php }
            }
    ?>
<?php
get_footer();
