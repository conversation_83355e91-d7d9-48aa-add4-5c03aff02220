.container-pb {
    padding-bottom: 5rem;
}

.downloadSection {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    align-content: center;
}

.downloadSection__item {
    background-color: #ececec;
    width: 40%;
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    align-content: center;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
}

.downloadSection__item-icon {
    padding: 10px 0px;
    width: 25%;
    float: left;
    text-align: center;
}

.downloadSection__item-icon i {
    color: #c49a6c;
    font-size: 80px;
}

.downloadSection__item-label {
    width: 75%;
    float: left;
    padding: 10px;
}

.downloadSection__item-label-anchor {
    color: #000;
    font-weight: bold;
    
}

.downloadSection__item-label-anchor:hover {
    color: #c49a6c;
    text-decoration: none;
}

.downloadSection__item-heading {
    background-color: #fff;
    width: 40%;
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    align-content: center;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
}

.downloadSection__item-heading h2 {
    color: var(--wcag-secondary-color);
    font-size: 40px;
    font-weight: 400;
    margin: 0px;
}