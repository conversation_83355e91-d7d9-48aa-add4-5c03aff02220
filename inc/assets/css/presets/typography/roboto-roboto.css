body {
	font-family: '<PERSON><PERSON>', sans-serif;
	margin: 0;
}

/* Headings

--------------------------------------------- */

h1, h2, h3, h4, h5, h6 {
	font-family: '<PERSON>o', sans-serif;
	margin: 0 0 20px;
}
.comment-respond h3, .entry-comments h3 {
	margin-bottom: 3.750rem;
	margin-top: 6.25rem;
}
p, pre, table, form {
	margin: 0 0 0.857rem;
}
.navbar-brand {
	padding-top: 0.25rem;
}
.navbar-brand>a {
	font-family: "Roboto", sans-serif;
}
#masthead .navbar-nav>li a {
	border-bottom-width: 0;
	display: block;
	font-family: "Roboto", sans-serif;
	padding: 10px;
	text-transform: uppercase;
}
div#page-sub-header h1 {
	font-family: "Roboto", sans-serif;
	margin-bottom: 0;
	text-transform: none;
	padding-top: 0;
}
div#page-sub-header p {
	max-width: 100%;
}
.page .entry-title, .single .entry-title, .blog .entry-title {
	padding: 0;
}
#secondary .widget-title {
	font-family: "Roboto", sans-serif;
	text-transform: uppercase;
}
.entry-meta {
	font-family: 'Roboto', sans-serif;
}
.post-thumbnail {
	margin-bottom: 1.875rem;
}