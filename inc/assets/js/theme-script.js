jQuery( function ( $ ) {
    'use strict';
    // here for each comment reply link of WordPress
    $( '.comment-reply-link' ).addClass( 'btn btn-primary' );

    // here for the submit button of the comment reply form
    $( '#commentsubmit' ).addClass( 'btn btn-primary' );

    // The WordPress Default Widgets
    // Now we'll add some classes for the WordPress default widgets - let's go

    // the search widget
    $( '.widget_search input.search-field' ).addClass( 'form-control' );
    $( '.widget_search input.search-submit' ).addClass( 'btn btn-default' );
    $( '.variations_form .variations .value > select' ).addClass( 'form-control' );
    $( '.widget_rss ul' ).addClass( 'media-list' );

    $( '.widget_meta ul, .widget_recent_entries ul, .widget_archive ul, .widget_categories ul, .widget_nav_menu ul, .widget_pages ul, .widget_product_categories ul' ).addClass( 'nav flex-column' );
    $( '.widget_meta ul li, .widget_recent_entries ul li, .widget_archive ul li, .widget_categories ul li, .widget_nav_menu ul li, .widget_pages ul li, .widget_product_categories ul li' ).addClass( 'nav-item' );
    $( '.widget_meta ul li a, .widget_recent_entries ul li a, .widget_archive ul li a, .widget_categories ul li a, .widget_nav_menu ul li a, .widget_pages ul li a, .widget_product_categories ul li a' ).addClass( 'nav-link' );

    $( '.widget_recent_comments ul#recentcomments' ).css( 'list-style', 'none').css( 'padding-left', '0' );
    $( '.widget_recent_comments ul#recentcomments li' ).css( 'padding', '5px 15px');

    $( 'table#wp-calendar' ).addClass( 'table table-striped');

    // Adding Class to contact form 7 form
    $('.wpcf7-form-control').not(".wpcf7-submit, .wpcf7-acceptance, .wpcf7-file, .wpcf7-radio").addClass('form-control');
    $('.wpcf7-submit').addClass('btn btn-primary');

    // Adding Class to Woocommerce form
    $('.woocommerce-Input--text, .woocommerce-Input--email, .woocommerce-Input--password').addClass('form-control');
    $('.woocommerce-Button.button').addClass('btn btn-primary mt-2').removeClass('button');

    $('ul.dropdown-menu [data-toggle=dropdown]').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        $(this).parent().siblings().removeClass('open');
        $(this).parent().toggleClass('open');
    });

    // Fix woocommerce checkout layout
    $('#customer_details .col-1').addClass('col-12').removeClass('col-1');
    $('#customer_details .col-2').addClass('col-12').removeClass('col-2');
    $('.woocommerce-MyAccount-content .col-1').addClass('col-12').removeClass('col-1');
    $('.woocommerce-MyAccount-content .col-2').addClass('col-12').removeClass('col-2');

    // Add Option to add Fullwidth Section
    function fullWidthSection(){
        var screenWidth = $(window).width();
        if ($('.entry-content').length) {
            var leftoffset = $('.entry-content').offset().left;
        }else{
            var leftoffset = 0;
        }
        $('.full-bleed-section').css({
            'position': 'relative',
            'left': '-'+leftoffset+'px',
            'box-sizing': 'border-box',
            'width': screenWidth,
        });
    }
    fullWidthSection();
    $( window ).resize(function() {
        fullWidthSection();
    });

    // Allow smooth scroll
    $('.page-scroller').on('click', function (e) {
        e.preventDefault();
        var target = this.hash;
        var $target = $(target);
        $('html, body').animate({
            'scrollTop': $target.offset().top
        }, 1000, 'swing');
    });

    // Close navbar menu on ESC key press
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('#main-nav').hasClass('show')) {
            $('#main-nav').collapse('hide');
        }
    });

    // Contact Form 7 Real-time Validation
    function validateCF7Field(field) {
        var $field = $(field);
        var fieldType = $field.attr('type') || $field.prop('tagName').toLowerCase();
        var fieldValue = $field.val().trim();
        var isRequired = $field.hasClass('wpcf7-validates-as-required');
        var isAcceptance =  $field.hasClass('wpcf7-acceptance');
        var isValid = true;
        var errorMessage = '';

        // Remove existing error states
        $field.removeClass('wpcf7-not-valid is-invalid');
        $field.siblings('.wpcf7-not-valid-tip').remove();

        // Required field validation
        if ((isRequired && fieldValue === '') || (isAcceptance && !$field.is(':checked'))) {
            isValid = false;
            errorMessage = 'To pole jest wymagane.';
        }
        // Email validation
        else if ($field.hasClass('wpcf7-validates-as-email') && fieldValue !== '') {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(fieldValue)) {
                isValid = false;
                errorMessage = 'Proszę wprowadzić prawidłowy adres e-mail <NAME_EMAIL>.';
            }
        }
        // Phone validation
        else if ($field.hasClass('wpcf7-validates-as-tel') && fieldValue !== '') {
            var phoneRegex = /^[\+]?[0-9\s\-\(\)]{9,}$/;
            if (!phoneRegex.test(fieldValue)) {
                isValid = false;
                errorMessage = 'Proszę wprowadzić prawidłowy numer telefonu w formacie XXX XXX XXX.';
            }
        }
        // URL validation
        else if ($field.hasClass('wpcf7-validates-as-url') && fieldValue !== '') {
            var urlRegex = /^https?:\/\/.+/;
            if (!urlRegex.test(fieldValue)) {
                isValid = false;
                errorMessage = 'Proszę wprowadzić prawidłowy adres URL.';
            }
        }

        // Apply error state if invalid
        if (!isValid && !isAcceptance) {
            $field.addClass('wpcf7-not-valid is-invalid');
            $field.after('<span class="wpcf7-not-valid-tip" role="alert">' + errorMessage + '</span>');
        }

        if (!isValid && isAcceptance) {
            $field.find('label').addClass('wpcf7-not-valid is-invalid');
            $field.after('<span class="wpcf7-not-valid-tip" role="alert">' + errorMessage + '</span>');
        }

        return isValid;
    }

    // Bind blur event to all CF7 form fields
    $(document).on('blur', '.wpcf7-form-control:not(.wpcf7-submit)', function() {
        validateCF7Field(this);
    });

    // Handle form submission
    $(document).on('click', '.wpcf7-submit', function(e) {
        var $form = $(this).closest('.wpcf7-form');
        var $fields = $form.find('.wpcf7-form-control:not(.wpcf7-submit)');
        var hasErrors = false;
        var firstErrorField = null;

        // Validate all fields
        $fields.each(function() {
            if (!validateCF7Field(this)) {
                hasErrors = true;
                if (firstErrorField === null) {
                    firstErrorField = this;
                }
            }
        });

        // If there are errors, prevent submission and focus on first error
        if (hasErrors) {
            e.preventDefault();
            if (firstErrorField) {
                $(firstErrorField).focus();
                // Scroll to the field if it's not visible
                $('html, body').animate({
                    scrollTop: $(firstErrorField).offset().top - 100
                }, 500);
            }
            return false;
        }
    });

    // Clear validation errors when user starts typing
    $(document).on('input', '.wpcf7-form-control.wpcf7-not-valid', function() {
        var $field = $(this);
        $field.removeClass('wpcf7-not-valid is-invalid');
        $field.siblings('.wpcf7-not-valid-tip').remove();
    });

    $(document).ready(function() {
        $('.wpcf7-form-control.wpcf7-acceptance input').prop('required', true);
    });
});
