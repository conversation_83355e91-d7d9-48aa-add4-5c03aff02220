<?php
/**
 * Template Name: <PERSON><PERSON><PERSON> budynki single
 */

get_header(); ?>


  <div class="mu-page-header"
       style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'large') ?>');">
    <div class="container">
      <div class="row">
        <div class="col-sm-12">
          <p class="mu-page-header--pagename"><?php the_title(); ?></p>
          <h1>Opis budynku</h1>
        </div>
      </div>
    </div>
  </div>
  <div class="container mu-building-description-container">
  <div class="row">
    <div class="col-lg-12">
      <img alt="" class="mu-building-thumbnail" src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'large') ?>">
    </div>
  </div>
<?php if (have_rows('sekcja_-_opis')):
  while (have_rows('sekcja_-_opis')): the_row();
    $podtytul = get_sub_field('podtytul');
    $tytul = get_sub_field('tytul');
    $tekst = get_sub_field('tekst');
    $galeria = get_sub_field('galeria'); ?>
    <div class="row">
      <div class="col-lg-6">
        <?php if ($podtytul): ?>
          <p class="mu-sep-small-title mu-seconday-color"><?php echo $podtytul; ?></p>
        <?php endif; ?>
        <?php if ($tytul): ?>
          <p class="mu-heading"><?php echo $tytul; ?></p>
        <?php endif; ?>
      </div>
    </div>
    <div class="row">
      <div class="col-lg-6 offset-lg-3">
        <?php if ($tekst): ?>
          <div class="mu-building-desc">
            <?php echo $tekst; ?>
          </div>
        <?php endif; ?>
      </div>
    </div>

    <?php if ($galeria): ?>
      <div class="row galeria">
        <?php foreach ($galeria as $item) : ?>
          <div class="col-lg-4 col-md-6">
            <a href="<?= $item['url'] ?>" data-lightbox="lightbox">
              <img src="<?= $item['url'] ?>" alt="<?= $item['title'] ?>">
            </a>
          </div>
        <?php endforeach ?>
      </div>
    <?php endif; ?>

  <?php endwhile;
endif; ?>
<?php if (have_rows('sekcja_-_dlaczego_warto')):
  while (have_rows('sekcja_-_dlaczego_warto')): the_row();
    $podtytul = get_sub_field('podtytul');
    $tytul = get_sub_field('tytul'); ?>
    <div class="mu-building-features-container">
      <div class="container">
        <div class="row">
          <div class="col-lg-8">
            <?php if ($podtytul): ?>
              <p class="mu-sep-small-title mu-seconday-color"><?php echo $podtytul; ?></p>
            <?php endif; ?>
            <?php if ($tytul): ?>
              <p class="mu-heading"><?php echo $tytul; ?></p>
            <?php endif; ?>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12">
            <div class="mu-building-features-icons">
              <?php if (have_rows('ikony')):
                while (have_rows('ikony')): the_row();
                  $ikona = get_sub_field('ikona');
                  $podpis = get_sub_field('podpis'); ?>
                  <div class="mu-building-features-icons--item">
                    <?php if ($ikona): ?>
                      <img alt="" src="<?php echo $ikona; ?>">
                    <?php endif; ?>
                    <?php if ($podpis):
                      echo "<p>$podpis</p>";
                    endif; ?>
                  </div>
                <?php endwhile;
              endif; ?>
            </div>
          </div>
        </div>
      </div>
    </div>
  <?php endwhile;
endif; ?>
<?php if (have_rows('sekcja_-_mapa')):
  while (have_rows('sekcja_-_mapa')): the_row();
    $podtytul = get_sub_field('podtytul');
    $mapa = get_sub_field('mapa'); ?>
    <div class="container pt-5 pb-5">
      <div class="row">
        <div class="col-lg-12 pt-5 pb-5">
          <?php if ($podtytul): ?>
            <p class="mu-sep-small-title mu-seconday-color pb-5"><?php echo $podtytul; ?></p>
          <?php endif; ?>
          <?php if ($mapa):
            echo $mapa;
          endif; ?>
        </div>
      </div>
    </div>
  <?php endwhile;
endif; ?>
<?php
get_footer();

