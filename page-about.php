<?php
/**
* Template Name: O nas
 */

get_header(); ?>

	

    <div class="mu-page-header" style="background-image: url('/wp-content/uploads/2021/09/header_ofirmie.png');">
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <p class="mu-page-header--pagename">Muchowiecka apartments</p>
                    <h1>O nas</h1>
                </div>
            </div>
        </div>
    </div>

    <?php if( have_rows('sekcja_-_o_firmie') ): 
            while( have_rows('sekcja_-_o_firmie') ): the_row(); 
                $podtytul = get_sub_field('podtytul'); 
                $tytul = get_sub_field('tytul'); 
                $tekst = get_sub_field('tekst'); 
                $przycisk = get_sub_field('przycisk'); ?>
                <div class="container mu-about-content" id="main-content">
                    <div class="row">
                        <div class="col-sm-12">
                            <?php if($podtytul): ?>
                                <p class="mu-sep-small-title mu-seconday-color"><?php echo $podtytul; ?></p>
                            <?php endif; ?>
                            <?php if($tytul): ?>
                                <h2 class="mu-heading"><?php echo $tytul; ?></h2>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="mu-abount-content-container">
                        <div class="mu-abount-content-container--column">
                            <div class="mu-light-content mt-5">
                                <?php if($tekst): 
                                    echo $tekst;
                                endif; ?>
                                <?php if($przycisk): ?>
                                    <a class="mu-primary-btn mu-primary-btn-color-main" aria-label="Zobacz ofertę wynajmu" href="<?php echo $przycisk['url']; ?>"><?php echo $przycisk['title']; ?> <i class="fas fa-long-arrow-alt-right"></i></a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="mu-abount-content-container--column">
                        <?php if( have_rows('lista_lokali_w_uzyciu') ): 
                            while( have_rows('lista_lokali_w_uzyciu') ): the_row(); 
                                $tytul = get_sub_field('tytul'); 
                                $tekst = get_sub_field('tekst'); ?>
                                    <div class="mu-light-content mu-about-bg">
                                        <?php if($tytul): ?>
                                            <p class="mu-about-bg--heading"><?php echo $tytul; ?></p>
                                        <?php endif; ?>
                                        <?php if($tekst): 
                                            echo $tekst; 
                                        endif; ?>
                                    </div>
                                    <div class="mu-btns-container mb-5">
                                    <?php if( have_rows('przyciski') ): 
                                        while( have_rows('przyciski') ): the_row(); 
                                            $przycisk = get_sub_field('przycisk'); ?>
                                            <?php if($przycisk): ?>
                                                <a class="mu-primary-btn mu-primary-btn-color-main" href="<?php echo $przycisk['url'];?>"><?php echo $przycisk['title']; ?> <i class="fas fa-long-arrow-alt-right"></i></a>
                                            <?php endif; ?>
                                        <?php endwhile;
                                        endif; ?>
                                    </div>
                        <?php endwhile;
                        endif; ?>
                        <div class="mu-light-content mu-about-bg">
                            <p class="mu-about-bg--heading">Dokumenty do pobrania</p>
                            <ul class="list-docs">
                            <?php if( have_rows('dokumenty-do_pobrania') ):
                            while( have_rows('dokumenty-do_pobrania') ): the_row(); 
                                $nazwa = get_sub_field('nazwa'); 
                                $plik = get_sub_field('plik'); 
                                ?>
                                <?php if($plik): ?>
                                    <li>
                                        <a href="<?php echo $plik['url'];?>" download>
                                            <i class="far fa-file-pdf" aria-hidden="true"></i>
                                            <?php echo $nazwa ?> [PDF]
                                            <span class="sr-only">Pobierz plik PDF</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endwhile;
                            endif; ?>
                            </ul>
                        </div>
                        </div>
                    </div>
                </div>
            <?php endwhile;
        endif; ?>
        <?php if( have_rows('sekcja_-_dane_firmy') ): 
            while( have_rows('sekcja_-_dane_firmy') ): the_row(); 
                $naglowek = get_sub_field('naglowek'); 
                $lewa_kolumna = get_sub_field('lewa_kolumna'); 
                $prawa_kolumna = get_sub_field('prawa_kolumna'); ?>
                <div class="mu-about-contact-data" style="background-image:url('/wp-content/uploads/2020/08/mu-texture-bg.png');">
                    <div class="container">         
                        <div class="mu-about-contact-data--container">
                            <?php if($naglowek): ?>
                                <h2><?php echo $naglowek; ?></h2>
                            <?php endif; ?>
                            <div class="mu-about-contact-data-container--column">
                                <?php if($lewa_kolumna): 
                                    echo $lewa_kolumna; 
                                endif; ?>
                            </div>
                            <div class="mu-about-contact-data-container--column">  
                                <?php if($prawa_kolumna): 
                                    echo $prawa_kolumna; 
                                endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
        <?php endwhile;
        endif; ?>
        <?php if( have_rows('sekcja_-_wspolpracuja_z_nami') ): 
            while( have_rows('sekcja_-_wspolpracuja_z_nami') ): the_row(); 
                $podtytul = get_sub_field('podtytul'); 
                $tytul = get_sub_field('tytul'); ?>
                <div class="container mu-about-partners">
                    <?php if($podtytul): ?>
                        <p class="mu-sep-small-title"><?php echo $podtytul; ?></p>
                    <?php endif; ?>
                    <?php if($tytul): ?>
                        <h2 class="mu-heading"><?php echo $tytul; ?></h2>
                    <?php endif; ?>
                    <div class="mu-about-partners--carousel vertical-mid-slick-nav">
                    <?php if( have_rows('karuzela_logo') ): 
                        while( have_rows('karuzela_logo') ): the_row(); 
                            $logo = get_sub_field('logo');
                            $alt = get_sub_field('tekst_alternatywny');
                            ?>
                            <img alt="<?= $alt ?>" src="<?php echo $logo; ?>">
                        <?php endwhile;
                        endif; ?>
                    </div>
                </div>
                <script>
                    (function($) {
                        $( document ).ready(function() {
                            $('.mu-about-partners--carousel').slick({
                                infinite: true,
                                dots: true,
                                arrows: true,
                                slidesToShow: 5,
                                slidesToScroll: 4,
                                responsive: [
                                    {
                                    breakpoint: 1024,
                                        settings: {
                                            slidesToShow: 4,
                                            slidesToScroll: 4,
                                            infinite: true,
                                            dots: true,
                                            arrows: false
                                        }
                                    },
                                    {
                                    breakpoint: 600,
                                        settings: {
                                            slidesToShow: 3,
                                            slidesToScroll: 3
                                        }
                                    },
                                    {
                                    breakpoint: 480,
                                        settings: {
                                            slidesToShow: 3,
                                            slidesToScroll: 3
                                        }
                                    }
                                ]
                            });
                        });
                    })(jQuery);
                </script>
                <?php endwhile;
                endif; ?>
<?php
get_footer();
