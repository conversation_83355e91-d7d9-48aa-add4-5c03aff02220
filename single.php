<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package WP_Bootstrap_Starter
 */

get_header(); ?>

	<div class="mu-page-header" style="background-image: url('/wp-content/uploads/2020/08/mu-offer-heading.png');">
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <p class="mu-page-header--pagename">Aktualności</p>
                    <h1><?php the_title(); ?></h1>
                </div>
            </div>
        </div>
	</div>
	
	<div class="container">

		<section id="primary" class="content-area col-lg-12">
			<main id="main" class="site-main" role="main">

			<?php
			while ( have_posts() ) : the_post(); ?>

				<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
					<div class="mu-single-meta"><?php the_category( ', ' ); ?> / <?php the_time('F jS, Y'); ?></div>
					<div class="entry-content">
						<?php
						if ( is_single() ) :
							the_content();
						else :
							the_content( __( 'Continue reading <span class="meta-nav">&rarr;</span>', 'muchowiecka' ) );
						endif;
				
							wp_link_pages( array(
								'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'muchowiecka' ),
								'after'  => '</div>',
							) );
						?>
					</div><!-- .entry-content -->
				</article><!-- #post-## -->
			
			<?php endwhile; // End of the loop. ?>

			</main><!-- #main -->
		</section><!-- #primary -->

	</div>

<?php
get_footer();
