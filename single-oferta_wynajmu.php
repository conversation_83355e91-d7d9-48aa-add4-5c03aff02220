<?php
/**
* Template Name: Szczegóły inwestycji
 */

get_header(); ?>
<!-- SLIDER BEGIN -->
	<?php if( have_rows('sekcja_-_slider') ):
        while( have_rows('sekcja_-_slider') ): the_row(); ?>
                <?php if( have_rows('slajd') ): ?>
                <div class="mu-page-header-carousel">
                    <?php while( have_rows('slajd') ): the_row();
                        $zdjecie = get_sub_field('zdjecie');
                        $podtytul = get_sub_field('podtytul');
                        $tytul = get_sub_field('tytul');
                        $przycisk = get_sub_field('przycisk'); ?>
                    <div>
                        <div class="mu-page-header" style="background-image: url('<?php echo $zdjecie; ?>');">
                            <div class="container">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="mu-homepage-header">
                                            <?php if($podtytul): ?>
                                                <p class="mu-homepage-header--subtitle"><?php echo $podtytul; ?></p>
                                            <?php endif; ?>
                                            <?php if($tytul): ?>
                                                <h1><?php echo $tytul; ?></h1>
                                            <?php endif; ?>
                                            <?php if($przycisk): ?>
                                                <a href="<?php echo $przycisk['url']; ?>" target="<?php echo $przycisk['target']; ?>" class="mu-homepage-header--btn"><?php echo $przycisk['title']; ?> <i class="fas fa-long-arrow-alt-right"></i></a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endwhile; ?>
                    </div>
                <?php else: ?>
                <div class="mu-page-header" style="background-image: url('/wp-content/uploads/2020/08/mu-building-item-heading.png');">
                    <div class="container">
                        <div class="row">
                            <div class="col-sm-12">
                                <h1 class="mu-page-header--pagename"><?php the_title(); ?></h1>
                                <p>Szczegóły inwestycji</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
        <?php endwhile; ?>
<?php endif; ?>
    <div class="container mt-5">
        <div class="row">
            <div class="offset-lg-1 col-lg-6">
                <h2 class="mu-sep-small-title mu-seconday-color mb-5 text-uppercase">Galeria zdjęć i szczegóły oferty</h2>
            </div>
        </div>
        <?php if( have_rows('sekcja_-_galeria') ): 
            while( have_rows('sekcja_-_galeria') ): the_row(); ?>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mu-building-gallery--items">
                            <?php if( have_rows('zdjecia') ): 
                                while( have_rows('zdjecia') ): the_row(); 
                                    $zdjecie = get_sub_field('zdjecie'); 
                                    $zdjecieThumb = $zdjecie['sizes']['thumbnail'];
                                    $zdjecieLarge = $zdjecie['sizes']['large'];
                                    $galleryContent = '<a href="'.$zdjecieLarge.'"><img class="muItemImage" alt="Zdjęcie przedstawiające oferowany lokal" data-full-img="'.$zdjecieLarge.'" src="'.$zdjecieThumb.'"></a>';
                                    if ( function_exists('slb_activate') ) {
                                        $galleryContent = slb_activate($galleryContent);
                                    }
                                    echo $galleryContent;
                                ?>
                                <?php endwhile;
                            endif; ?>
                        </div>
                    </div>
                </div>
        <?php endwhile;
        endif; ?>
        <?php if( have_rows('sekcja_-_opis') ): 
                while( have_rows('sekcja_-_opis') ): the_row(); 
                    $tekst = get_sub_field('tekst'); ?>
                    <div class="mu-building-details-info">
                        <div class="row">
                            <div class="offset-lg-1 col-lg-4">
                                Zapraszamy do zapoznania się z naszą ofertę na naszych pozostałych stronach internetowych
                                <?php if( have_rows('przyciski') ): 
                                    while( have_rows('przyciski') ): the_row(); 
                                        $przycisk = get_sub_field('przycisk'); ?>
                                    <?php if($przycisk): ?>
                                        <a class="mu-primary-btn mu-primary-btn-color-1" target="<?php echo $przycisk['target']; ?>" href="<?php echo $przycisk['url']; ?>"><?php echo $przycisk['title']; ?> <i class="fas fa-long-arrow-alt-right"></i></a>
                                    <?php endif; ?>
                                <?php endwhile;
                                endif; ?>
                            </div>
                            <div class="col-lg-7 mu-building-details-info-desc">
                                <?php if($tekst): echo $tekst; endif; ?>
                            </div>
                        </div>
                    </div>
            <?php endwhile;
        endif; ?>
    </div>
    <?php if( have_rows('sekcja_-_mapa') ): 
            while( have_rows('sekcja_-_mapa') ): the_row(); 
                $podtytul = get_sub_field('podtytul'); 
                $kod_iframe_mapy = get_sub_field('kod_iframe_mapy'); ?>
                <div class="mu-building-map-container">
                    <?php if($podtytul): ?>
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-8">
                                    <p class="mu-sep-small-title mu-seconday-color"><?php echo $podtytul; ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php if($kod_iframe_mapy): ?>
                        <div class="mu-building-map">
                            <?php echo $kod_iframe_mapy; ?>
                        </div>
                    <?php endif; ?>
                </div>
    <?php endwhile;
    endif; ?>
    <?php if( have_rows('sekcja_-_kontakt') ): 
            while( have_rows('sekcja_-_kontakt') ): the_row(); 
                $podtytul = get_sub_field('podtytul'); 
                $tytul = get_sub_field('tytul'); 
                $tekst = get_sub_field('tekst'); 
                $zdjecie = get_sub_field('zdjecie'); 
                $imie_i_nazwisko = get_sub_field('imie_i_nazwisko'); 
                $stanowisko = get_sub_field('stanowisko'); 
                $telefon = get_sub_field('telefon'); 
                $adres_email = get_sub_field('adres_e-mail'); ?>
                <div class="container mu-building-details-contact-content">
                    <div class="mu-contact-content-container mt-5">
                        <div class="mu-contact-content-container--column">
                            <?php if($podtytul): ?>
                                <p class="mu-sep-small-title"><?php echo $podtytul; ?></p>
                            <?php endif; ?>
                            <?php if($tytul): ?>
                                <p class="mu-heading mu-secondary-color"><?php echo $tytul; ?></p>
                            <?php endif; ?>
                            <?php if($tekst): ?>
                            <div class="mu-light-content mt-5">
                                <?php echo $tekst; ?>
                            </div>
                            <?php endif; ?>
                            <div class="mu-contact-person--item">
                                <?php if($zdjecie): ?>
                                    <img alt="" src="<?php echo $zdjecie; ?>">
                                <?php endif; ?>
                                <div class="mu-contact-person-item--content">
                                    <?php if($imie_i_nazwisko): ?>
                                        <p class="mu-contact-person-item-content--name"><?php echo $imie_i_nazwisko; ?></p>
                                    <?php endif; ?>
                                    <?php if($stanowisko): ?>
                                        <p class="mu-contact-person-item-content--role"><?php echo $stanowisko; ?></p>
                                    <?php endif; ?>
                                    <?php if($telefon): ?>
                                        <p class="mu-contact-person-item-content--phone">Telefon: <?php echo $telefon; ?></p>
                                    <?php endif; ?>
                                    <?php if($adres_email): ?>
                                        <p class="mu-contact-person-item-content--mail">Adres e-mail: <?php echo $adres_email; ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="mu-contact-content-container--column mu-dark-content">
                            <?php echo do_shortcode('[contact-form-7 id="23" title="Formularz kontaktowy"]'); ?>
                        </div>
                    </div>
                </div>
    <?php endwhile;
    endif; ?>
<script>
	(function($) {
		$( document ).ready(function() {
			$('.mu-page-header-carousel').slick({
				infinite: false,
				dots: false,
				arrows: true,
				slidesToShow: 1,
				slidesToScroll: 1
			});
			$('.mu-our-offer-carousel').slick({
				infinite: true,
				dots: true,
				arrows: false,
				slidesToShow: 1,
				slidesToScroll: 1
			});
			$('.mu-buildings-slider-carousel').slick({
				infinite: true,
				dots: false,
				arrows: true,
				slidesToShow: 1,
				slidesToScroll: 1
			});
		});
	})(jQuery);
</script>
<?php
get_footer();
?>
<script>
    // (function($) {
    //     $( document ).ready(function() {
    //         $( ".muItemImage" ).click(function() {
    //             var $this = $(this);
    //             var newSrc = $(this).data('full-img');
    //             var oldSrc = $('#muItemActiveImage').attr('src');
    //             $('#muItemActiveImage').fadeOut(400, function() {
    //                 $('img[src="' + oldSrc + '"]#muItemActiveImage').attr('src', newSrc);
    //             }).fadeIn(400);
    //         });
    //     });
    // })(jQuery);
</script>