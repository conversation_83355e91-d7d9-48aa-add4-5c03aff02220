body {
    margin: 0;
    -webkit-font-smoothing: auto;
    direction: rtl;
    unicode-bidi: embed;
    text-align: right;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section,
summary {
    display: block;
}

audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline;
}

audio:not([controls]) {
    display: none;
    height: 0;
}

[hidden],
template {
    display: none;
}

a {
    background-color: transparent;
}

a:active,
a:hover {
    outline: 0;
}

dfn {
    font-style: italic;
}

mark {
    background: #ff0;
    color: #000;
}

small {
    font-size: 80%;
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

img {
    border: 0;
}

svg:not(:root) {
    overflow: hidden;
}

figure {
    margin: 1em 2.5rem;
}

hr {
    box-sizing: content-box;
    height: 0;
}

button {
    overflow: visible;
}

button,
select {
    text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}

button[disabled],
html input[disabled] {
    cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}

input {
    line-height: normal;
}

input[type="checkbox"],
input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    height: auto;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}

legend {
    border: 0;
    padding: 0;
}

textarea {
    overflow: auto;
}

optgroup {
    font-weight: bold;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

td,
th {
    padding: 0;
}

/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
h1, h2, h3, h4, h5, h6 {
    clear: both;
    color: #24292e;
    font-weight: 600;
    margin-top: 24px;
    margin-bottom: 15px;
}
h1 {
    font-size: 32px;
    padding-bottom: 10px;
}
h2 {
    font-size: 24px;
    padding-bottom: 0.3em;
    line-height: 1.25;
}
h3 {
    font-size: 18px;
    line-height: 25px;
}
h4 {
    font-size: 16px;
    line-height: 20px;
}
h5 {
    font-size: 14px;
    line-height: 17.5px;
}

p {
    margin-bottom: 1.5em;
}
h1.entry-title {
    font-size: 21px;
    border-bottom: 1px solid #eaecef;
}
h2.entry-title {
    border-bottom: 1px solid #eaecef;
}
h3.widget-title {
    font-size: 1.2rem;
}

/*--------------------------------------------------------------
# Elements
--------------------------------------------------------------*/
body {
    background: #fff;
    /* Fallback for when there is no custom background color defined. */
}

img {
    height: auto;
    /* Make sure images are scaled correctly. */
    max-width: 100%;
    /* Adhere to container width. */
}

figure {
    margin: 1em 0;
    /* Extra wide images within figure tags don't overflow the content area. */
}

table {
    margin: 0 0 1.5em;
    width: 100%;
}

/*--------------------------------------------------------------
# Forms
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Navigation
--------------------------------------------------------------*/
header#masthead {
    margin-bottom: 0;
    background-color: #563d7c ;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.05), inset 0 -1px 0 rgba(0,0,0,.1);
    padding: .74rem 1rem;
}

.navbar-brand > a {
    color: rgba(0, 0, 0, 0.9);
    font-size: 1.1rem;
    outline: medium none;
    text-decoration: none;
    color: #fff;
    font-weight: 700;
}

.navbar-brand > a:visited, .navbar-brand > a:hover {
    text-decoration: none;
}

#page-sub-header {
    position: relative;
    padding-top: 5rem;
    padding-bottom: 0;
    text-align: center;
    font-size: 1.25rem;
    background-size: cover !important;
}

body:not(.theme-preset-active) #page-sub-header h1 {
    line-height: 1.6;
    font-size: 4rem;
    color: #563e7c;
    margin: 0 0 1rem;
    border: 0;
    padding: 0;
}

#page-sub-header p {
    margin-bottom: 0;
    line-height: 1.4;
    font-size: 1.25rem;
    font-weight: 300;
}
body:not(.theme-preset-active) #page-sub-header p {
    color: #212529;
}
a.page-scroller {
    color: #333;
    font-size: 2.6rem;
    display: inline-block;
    margin-top: 2rem;
}

@media screen and (min-width: 768px) {
    body:not(.theme-preset-active) #page-sub-header h1 {
        font-size: 3.750rem;
    }
    body:not(.theme-preset-active) #page-sub-header {
        font-size: 1.25rem;
    }
}
@media screen and (min-width: 992px) {
    #page-sub-header p {
        max-width: 43rem;
        margin: 0 auto;
    }
}
/*--------------------------------------------------------------
## Links
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Menus
--------------------------------------------------------------*/
#masthead nav {
    padding-right: 0;
    padding-left: 0;
}

body:not(.theme-preset-active) #masthead .navbar-nav > li > a {
    color: #cdbfe3;
    padding: 0.5rem;
    font-weight: 500;
    font-size: 14px;
}

body:not(.theme-preset-active) #masthead .navbar-nav > li > a:hover,
body:not(.theme-preset-active) #masthead .navbar-nav > li.current_page_item > a {
    color: #fff;
    font-weight: 600;
    /*background: #f9f9f9;*/
}

.navbar-brand {
    height: auto;
}

.navbar-toggle .icon-bar {
    background: #000 none repeat scroll 100% 0;
}

.dropdown-menu .dropdown-toggle::after {
    border-bottom: 0.3em solid transparent;
    border-right: 0.3em solid;
    border-top: 0.3em solid transparent;
}

/* Small menu. */
.menu-toggle,
.main-navigation.toggled ul {
    display: block;
}

.dropdown-item {
    line-height: 1.2;
    padding-bottom: 0.313rem;
    padding-top: 0.313rem;
}

.dropdown-menu {
    min-width: 12.500rem;
}

.dropdown .open .dropdown-menu {
    display: block;
    right: 12.250em;
    top: 0;
}

.dropdown-menu .dropdown-item {
    white-space: normal;
    font-size: 14px;
}

@media screen and (min-width: 37.5em) {
    .menu-toggle {
        display: none;
    }
}
@media screen and (min-width: 769px) {
    .dropdown-menu li > .dropdown-menu {
        left: -9.875rem;
        top: 1.375rem;
    }
}
@media screen and (max-width: 991px) {
    .dropdown-menu {
        border: medium none;
        margin-right: 1.250rem;
        padding: 0;
    }

    .dropdown-menu li a {
        padding: 0;
    }

    #masthead .navbar-nav > li > a {
        padding-bottom: 0.625rem;
        padding-top: 0.313rem;
    }

    .navbar-light .navbar-toggler {
        border: medium none;
        outline: none;
    }
}
.site-main .comment-navigation,
.site-main .posts-navigation,
.site-main .post-navigation {
    margin: 0 0 1.5em;
    overflow: hidden;
}

.comment-navigation .nav-previous,
.posts-navigation .nav-previous,
.post-navigation .nav-previous {
    float: right;
    width: 50%;
}

.comment-navigation .nav-next,
.posts-navigation .nav-next,
.post-navigation .nav-next {
    float: left;
    text-align: left;
    width: 50%;
}
.comment-content.card-block {
    padding: 20px;
}

.navigation.post-navigation {
    padding-top: 1.875rem;
}

.post-navigation .nav-previous a,
.post-navigation .nav-next a {
    border: 1px solid #ddd;
    border-radius: 0.938rem;
    display: inline-block;
    padding: 0.313rem 0.875rem;
    text-decoration: none;
}

.post-navigation .nav-next a::after {
    content: " \2192";
}

.post-navigation .nav-previous a::before {
    content: "\2190 ";
}

.post-navigation .nav-previous a:hover,
.post-navigation .nav-next a:hover {
    background: #eee none repeat scroll 100% 0;
}

/*--------------------------------------------------------------
# Accessibility
--------------------------------------------------------------*/
/* Text meant only for screen readers. */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    word-wrap: normal !important;
    /* Many screen reader and browser combinations announce broken words as they would appear visually. */
}
.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    color: #21759b;
    display: block;
    font-size: 14px;
    font-size: 0.875rem;
    font-weight: bold;
    height: auto;
    right: 0.313rem;
    line-height: normal;
    padding: 0.938rem 1.438rem 0.875rem;
    text-decoration: none;
    top: 0.313rem;
    width: auto;
    z-index: 100000;
    /* Above WP toolbar. */
}

/* Do not show the outline on the skip link target. */
#content[tabindex="-1"]:focus {
    outline: 0;
}

/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/
.alignleft {
    display: inline;
    float: right;
    margin-left: 1.5em;
}

.alignright {
    display: inline;
    float: left;
    margin-right: 1.5em;
}

.aligncenter {
    clear: both;
    display: block;
    margin-right: auto;
    margin-left: auto;
}

a img.alignright {
    float: left;
    margin: 0.313rem 1.25rem 1.25rem 0;
}

a img.alignnone {
    margin: 0.313rem 0 1.25rem 1.25rem;
}

a img.alignleft {
    float: right;
    margin: 0.313rem 0 1.25rem 1.25rem;
}

a img.aligncenter {
    display: block;
    margin-right: auto;
    margin-left: auto;
}

.wp-caption.alignnone {
    margin: 0.313rem 0 1.25rem 1.25rem;
}

.wp-caption.alignleft {
    margin: 0.313rem 0 1.25rem 1.25rem;
}

.wp-caption.alignright {
    margin: 0.313rem 1.25rem 1.25rem 0;
}

/*--------------------------------------------------------------
# Clearings
--------------------------------------------------------------*/
.clear:before,
.clear:after,
.entry-content:before,
.entry-content:after,
.comment-content:before,
.comment-content:after,
.site-header:before,
.site-header:after,
.site-content:before,
.site-content:after,
.site-footer:before,
.site-footer:after {
    content: "";
    display: table;
    table-layout: fixed;
}

.clear:after,
.entry-content:after,
.comment-content:after,
.site-header:after,
.site-content:after,
.site-footer:after {
    clear: both;
}

/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
.widget {
    margin: 0 0 1.5em;
    font-size: 14px;
    /* Make sure select elements fit in widgets. */
}
.widget select {
    max-width: 100%;
}

.widget_search .search-form input[type="submit"] {
    display: none;
}

.nav > li > a:focus,
.nav > li > a:hover {
    background-color: #eee;
    text-decoration: none;
}
.half-rule {
    width: 6rem;
    margin: 2.5rem 0;
}

/*--------------------------------------------------------------
# Content
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Posts and pages
--------------------------------------------------------------*/
#content.site-content {
    padding-bottom: 3.75rem;
    padding-top: 4.125rem;
}

.sticky .entry-title::before {
    content: '\f08d';
    font-family: fontawesome;
    font-size: 1.563rem;
    right: -2.5rem;
    position: absolute;
    top: 0.375rem;
}

.sticky .entry-title {
    position: relative;
}

.single .byline,
.group-blog .byline {
    display: inline;
}

.page-content,
.entry-content,
.entry-summary {
    margin: 1.5em 0 0;
}

.page-links {
    clear: both;
    margin: 0 0 1.5em;
}

.page-template-blank-page .entry-content,
.blank-page-with-container .entry-content {
    margin-top: 0;
}

.post.hentry {
    margin-bottom: 4rem;
}

.posted-on, .byline, .comments-link {
    color: #9a9a9a;
}

.entry-title > a {
    color: inherit;
}

/*--------------------------------------------------------------
## Comments
--------------------------------------------------------------*/
.comment-content a {
    word-wrap: break-word;
}

.bypostauthor {
    display: block;
}

.comment-body .pull-left {
    padding-left: 0.625rem;
}

.comment-list .comment {
    display: block;
}

.comment-list {
    padding-right: 0;
}

.comments-title {
    font-size: 1.125rem;
}

.comment-list .pingback {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.563rem 0;
}

.comment-list .pingback:first-child {
    border: medium none;
}

/*--------------------------------------------------------------
# Infinite scroll
--------------------------------------------------------------*/
/* Globally hidden elements when Infinite Scroll is supported and in use. */
.infinite-scroll .posts-navigation,
.infinite-scroll.neverending .site-footer {
    /* Theme Footer (when set to scrolling) */
    display: none;
}

/* When Infinite Scroll has reached its end we need to re-display elements that were hidden (via .neverending) before. */
.infinity-end.neverending .site-footer {
    display: block;
}

/*--------------------------------------------------------------
# Media
--------------------------------------------------------------*/
.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
    border: none;
    margin-bottom: 0;
    margin-top: 0;
    padding: 0;
}

/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
    max-width: 100%;
}

/*--------------------------------------------------------------
## Captions
--------------------------------------------------------------*/
.wp-caption {
    background: #f1f1f1 none repeat scroll 100% 0;
    border: 1px solid #f0f0f0;
    max-width: 96%;
    padding: 0.313rem 0.313rem 0;
    text-align: center;
}
.wp-caption img[class*="wp-image-"] {
    border: 0 none;
    height: auto;
    margin: 0;
    max-width: 100%;
    padding: 0;
    width: auto;
}
.wp-caption .wp-caption-text {
    font-size: 0.688rem;
    line-height: 1.063rem;
    margin: 0;
    padding: 0.625rem;
}

.wp-caption-text {
    text-align: center;
}

/*--------------------------------------------------------------
## Galleries
--------------------------------------------------------------*/
.gallery {
    margin-bottom: 1.5em;
}

.gallery-item {
    display: inline-block;
    text-align: center;
    vertical-align: top;
    width: 100%;
}
.gallery-item .gallery-columns-2 {
    max-width: 50%;
}
.gallery-item .gallery-columns-3 {
    max-width: 33.33333%;
}
.gallery-item .gallery-columns-4 {
    max-width: 25%;
}
.gallery-item .gallery-columns-5 {
    max-width: 20%;
}
.gallery-item .gallery-columns-6 {
    max-width: 16.66667%;
}
.gallery-item .gallery-columns-7 {
    max-width: 14.28571%;
}
.gallery-item .gallery-columns-8 {
    max-width: 12.5%;
}
.gallery-item .gallery-columns-9 {
    max-width: 11.11111%;
}

.gallery-caption {
    display: block;
}

/*--------------------------------------------------------------
# Plugin Compatibility
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Woocommerce
--------------------------------------------------------------*/
.woocommerce-cart-form .shop_table .coupon .input-text {
    width: 8.313rem !important;
}

.variations_form .variations .value > select {
    margin-bottom: 0.625rem;
}

.woocommerce-MyAccount-content .col-1,
.woocommerce-MyAccount-content .col-2 {
    max-width: 100%;
}

/*--------------------------------------------------------------
## Elementor
--------------------------------------------------------------*/
.elementor-page article .entry-footer {
    display: none;
}

.elementor-page.page-template-fullwidth #content.site-content {
    padding-bottom: 0;
    padding-top: 0;
}

.elementor-page .entry-content {
    margin-top: 0;
}

/*--------------------------------------------------------------
## Visual Composer
--------------------------------------------------------------*/
.vc_desktop article .entry-footer {
    display: none;
}

.vc_desktop #content.site-content {
    padding-bottom: 0;
    padding-top: 0;
}

.vc_desktop .entry-content {
    margin-top: 0;
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
footer#colophon {
    font-size: 85%;
}
body:not(.theme-preset-active) footer#colophon {
    color: #99979c;
    background-color: #f7f7f7;
}

.copyright {
    font-size: 0.875rem;
    margin-bottom: 0;
    text-align: center;
}

.copyright a, footer#colophon a {
    color: inherit;
}

@media screen and (max-width: 767px) {
    #masthead .navbar-nav > li > a {
        padding-bottom: 0.938rem;
        padding-top: 0.938rem;
    }
}
/*--------------------------------------------------------------
# Media Query
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Notebook
--------------------------------------------------------------*/
@media only screen and (max-width: 1280px) {
    html {
        font-size: 95%;
    }
}
/*--------------------------------------------------------------
## Netbook
--------------------------------------------------------------*/
@media only screen and (max-width: 1024px) {
    html {
        font-size: 93%;
    }
}
/*--------------------------------------------------------------
## iPad
--------------------------------------------------------------*/
@media only screen and (max-width: 960px) {
    html {
        font-size: 90%;
    }
}
/*--------------------------------------------------------------
## iPad
--------------------------------------------------------------*/
@media only screen and (max-width: 768px) {
    html {
        font-size: 88%;
    }
}
/*--------------------------------------------------------------
## iPad
--------------------------------------------------------------*/
@media only screen and (max-width: 480px) {
    html {
        font-size: 86%;
    }
}