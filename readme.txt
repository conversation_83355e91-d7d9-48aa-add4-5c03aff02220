=== WP Bootstrap Starter ===

Tags: blog, custom-menu, featured-images, threaded-comments, translation-ready, right-sidebar, custom-background, e-commerce, theme-options, sticky-post, full-width-template
Requires at least: 4.0
Tested up to: 4.7

Copyright (c) 2016 by Afterimage Designs (http://afterimagedesigns.com/)
This Theme is distributed under the terms of the GNU GPL.

License: GNU General Public License v2.0
License URI: http://www.gnu.org/licenses/gpl-2.0.html

== Description ==

The best WordPress starter theme based on the most powerful frameworks in the world: "_s" (by Automattic, the main people behind WordPress development), Twitter Bootstrap (the most popular HTML, CSS, and JS framework for developing responsive, mobile first projects on the web) and Font Awesome (or Fontawesome, a full suite of 675+ pictographic icons for easy scalable vector graphics on websites). This theme, like WordPress, is licensed under the GPL. You can use this theme as base for your next WordPress theme project and you are allowed to remove the link at the footer or do whatever you want. Your feedback on how we can continuously improve this WordPress Starter Theme Bootstrap will be highly appreciated. Page templates includes Right-sidebar (default page template), Left-Sidebar, Full-Width, Blank with container, Blank without container Page. Other features - Currently using Bootstrap v4.0.0-beta.2 , Widgetized footer area, WooCommerce ready, Compatible with Contact Form 7, Compatible with Visual Composer, Compatible with Elementor Page Builder. This theme will be an active project which we will update from time to time. Check this page regularly for the updates.


This theme, like WordPress, is licensed under the GPL. You can use this theme as base for your next WordPress theme project and you are allowed to remove the link at the footer or do whatever you want. Your feedback on how we can continuously improve this WordPress Starter Theme Bootstrap will be highly appreciated.

Page templates
* Right-sidebar (default page template)
* Left-Sidebar
* Full-Width
* Blank with container
* Blank without container Page

Other features:
* Currently using Bootstrap v4.3.1
* Font Awesome integrated
* Widgetized footer area
* WooCommerce ready
* Compatible with Contact Form 7
* Compatible with Visual Composer
* Compatible with Elementor Page Builder


This theme will be an active project which we will update from time to time. Check this page regularly for the updates.

== Installation ==

1. In your admin panel, go to Appearance > Themes and click the Add New button.
2. Click Upload and Choose File, then select the theme's .zip file. Click Install Now.
3. Click Activate to use your new theme right away.

== Frequently Asked Questions ==

= Does this theme support any plugins? =

WP Bootstrap Starter includes support for Infinite Scroll in Jetpack.

== Credits ==

* Based on Underscores http://underscores.me/, (C) 2012-2016 Automattic, Inc., [GPLv2 or later](https://www.gnu.org/licenses/gpl-2.0.html)
* normalize.css http://necolas.github.io/normalize.css/, (C) 2012-2016 Nicolas Gallagher and Jonathan Neal, [MIT](http://opensource.org/licenses/MIT)
* Bootstrap http://getbootstrap.com, (C) 2011-2015 Twitter, Inc., [MIT] (https://github.com/twbs/bootstrap/blob/master/LICENSE)
* WP Bootstrap Navwalker https://github.com/wp-bootstrap/wp-bootstrap-navwalker, [GPLv3](https://www.gnu.org/licenses/gpl-3.0.en.html)
* Font Awesome icons, Copyright (C) Dave Gandy License: SIL Open Font License, version 1.1. - http://fontawesome.io/

== Changelog ==

= 3.3.3 - March 02, 2020 =
* Update the wp_bootstrap_navwalker to v4.3.0
* /inc/wp_bootstrap_navwalker.php
* /functions.php, change the Popper JsDelivr to https://cdn.jsdelivr.net/npm/popper.js@1/dist/umd/popper.min.js
* /inc/assets/js/popper.js
* /inc/assets/js/popper.js.map
* /inc/assets/js/popper.min.js
* /inc/assets/js/popper.min.js.map
* Update woocommerce cart template to 3.8.0

= 3.3.2 - October 08, 2019 =
* Update the CDN feature on the theme customizer
* Make the CDN option on the customizer to only one setting
* Update the CDN link to jsDeliver
* Add prefetch loading on the jsDeliver scripts and stylesheet

= 3.3.1 - September 15, 2019 =
* Add new feature on the theme customizer
* Add an option on the theme customizer for the Bootstrap and Fontawesome to run on their respective CDN
* Update the local Fontawesome to v5.10.2
* Fix the issue of the wrong font adding on Litera preset stylesheet

= 3.2.7 - August 23, 2019 =
* Update the woocommerce/orderby.php to latest version
* Fix the sorting isssue on Woocommerce Shop Page

= 3.2.6 - August 15, 2019 =
* muchowiecka/woocommerce/cart/cart.php version 3.5.0 is out of date. The core version is 3.7.0
* Remove duplicate link - pingback tag on header.php
* Remove the condition on extras.php for the link - pingback tag

= 3.2.3 - June 24, 2019 =
* Update the Font Awesome to v5.8.2

= 3.2.2 - June 06, 2019 =
* Update the Bootstrap to v4.3 
* Update WooCommerce Templates 
* Update the wp_bootstrap_navwalker to v4.1.0
* Update the navigation dropdown styling
* Update the theme screenshot from .jpg to .png

= 3.1.0 - July 13, 2018 =
* Fix the dropdown color in all of the theme optin under customizer -> Preset Styles -> Theme Option
* Fix the size of the dropdown menu in all of the theme option
* Fix the color of the logo in all of the theme option
* Remove the border arround the footer widget

= 3.0.19 - July 10, 2018 =
* Change the protocol the of fontawesome to https

= 3.0.18 - June 27, 2018 =
* Adjust the theme-script.js to fix the layout of woocommerce checkout

= 3.0.17 - June 27, 2018 =
* Update fontawesome cdn to v5.1.0
* Fix the woocommerce checkout page layout

= 3.0.16 - June 7, 2018 =
* Update bootstrap files to v4.1.1
* Change the wrapper of the footer and footer widget to ".container" instead of ".container-fluid"

= 3.0.15 - May 7, 2018 =
* Remove the font awesome files
* Enqueue the fontawesome CDN

= 3.0.14 - May 2, 2018 =
* Update woocommerce files

= 3.0.13 - April 17, 2018 =
* Update the bootstrap files
* Fix the layout of the category widget to show the post count properly

= 3.0.12 - April 11, 2018 =
* Move the js file to the footer
* Create minified js file
* Fix the font color in the footer bar

= 3.0.11 - March 14, 2018 =
* Minor changes in the style.css to fix the visibility of the sub menu in other theme options

= 3.0.10 - March 13, 2018 =
* Change the navbar-toggler data-target attribute to #main-nav
* Add Installation welcome message
* Fix the font color of the submenu in mobile on hover
* Fix the path of the customizer.js which cause an error in the theme customizer

= 3.0.9 - February 27, 2018 =
* Fix the color of mobile menu
* Change the background of the sub menu in smaller screen from white to transparent

= 3.0.8 - February 23, 2018 =
* Add RTL support

= 3.0.7 - January 22, 2018 =
* Update Bootstrap 4 files

= 3.0.6 - January 17, 2018 =
* Remove the border in menu bar
* Fix the default values in customizer
* Upgrade popper.js
* Fix popper.js related warnings in console
* Integrate fontawesome 5
* Remove the unnecessary styling in the typography preset

= 3.0.5 - December 29, 2017 =
* Remove the underline in all of the H tags
* Fix the default wordpress gallery

= 3.0.4 - November 27, 2017 =
* Typography tweak

= 3.0.3 - November 24, 2017 =
* Fix the spacing of the page banner

= 3.0.2 - November 20, 2017 =
* Add the slide arrow in the header

= 3.0.1 - November 17, 2017 =
* Update the appearance the page banner
* Fix the text color of the presets

= 3.0.0 - November 3, 2017 =
* Update the header code base on bootstrap v4.0.0-beta.2
* Style the page base on v4.0.0-beta.2
* Add new typography preset

= 2.5.5 - October 27, 2017 =
* Fix the font in the typography preset
* Add new typography preset

= 2.5.4 - October 20, 2017 =
* Add new Preset
* Upgrade the bootstrap 4 alpha to bootstrap 4 beta

= 2.5.3 - October 18, 2017 =
* Add new Preset
* Fix the fonts in Poppins / Lora

= 2.5.2 - August 31, 2017 =
* Make the subheader visible to front page

= 2.5.1 - August 29, 2017 =
* Fix style of Poppins/Lora preset

= 2.5.0 - August 23, 2017 =
* Fix the H tag format of the  Montserrat / Merriweather preset

= 2.4.9 - August 3, 2017 =
* Remove the SASS directory since it contains developer file

= 2.4.8 - August 3, 2017 =
* Update the typography of Roboto / Roboto preset
* Update the letter spacing of Roboto / Roboto preset

= 2.4.7 - August 2, 2017 =
* Fix the js error in shop page

= 2.4.6 - August 1, 2017 =
* Fix the Display Site Title and Tagline option in header

= 2.4.5 - July 31, 2017 =
* Update the letter spacing of Poppins / Poppins preset

= 2.4.4 - July 26, 2017 =
* Update the typography of Poppins / Poppins preset

= 2.4.3 - July 25, 2017 =
* Add Arbutu Slab / Open Sans Preset
* Add Poppins / Poppins Preset
* Add Roboto / Roboto Preset
* Remove the pagination in the single attachment

= 2.4.2 - July 24, 2017 =
* Fix the fonts in the preset css

= 2.4.1 - July 22, 2017 =
* Add a 100% fullwidth class ".full-bleed-section"


= 2.4.0 - July 21, 2017 =
* Change the unit of the font from px to rem
* Make the typography responsive

= 2.3.9 - July 21, 2017 =
* Re arrange the customizer
* Remove the Banner Tagline color
* Remove the Banner Title color

= 2.3.8 - July 21, 2017 =
* Add the wordpress core css - https://codex.wordpress.org/CSS
* Update the template-tags.php to show only the date published

= 2.3.7 - July 20, 2017 =
* Change the arrangement of the customizer
* Add minimum height on the header banner
* Make the banner visible in after the theme installation

= 2.3.6 - July 18, 2017 =
* Change the arrangement of the customizer
* Add Header Color, Banner Title Color, Banner Tagline Color and Display Header Banner to the customizer
* Fix the errors found

= 2.3.5 - July 17, 2017 =
* Add preset style under customizer -> typography

= 2.3.4 - July 16, 2017 =
* Update the site info in footer

= 2.3.3 - July 14, 2017 =
* Update the content of the jumbotron to load default content after theme install

= 2.3.2 - July 14, 2017 =
* Update the site info in footer

= 2.3.1 - July 11, 2017 =
* Update the SASS files

= 2.3.0 - July 10, 2017 =
* Add Header Panel in customizer

= 2.2.9 - July 6, 2017 =
* Add SASS functionality

= 2.2.8 - July 6, 2017 =
* Add an option in customizer to change the jumbotron content in homepage

= 2.2.7 - July 3, 2017 =
* Update the style of woocommerce form

= 2.2.6 - June 30, 2017 =
* Update the woocommerce template

= 2.2.5 - June 30, 2017 =
* Change theme info

= 2.2.4 - June 29, 2017 =
* Change the look and feel of the menu to bootstrap 4
* Change the style the background color of the jumbotron
* Change the arrow of the submenu toarrow pointing to the right

= 2.2.3 - June 28, 2017 =
* Remove the underline below the sitename/logo

= 2.2.2 - June 28, 2017 =
* Change the theme info

= 2.2.1 - June 28, 2017 =
* Change the style of the of the sticky post
* Change the font to the default font of the Bootstrap 4
* Make the Default theme menu visible to all visitors

= 2.1.9 - June 28, 2017 =
* Add Spacing between comment and comment icon

= 2.1.8 - June 28, 2017 =
* Remove the border around the hamburger menu

= 2.1.7 - June 27, 2017 =
* Change theme info

= 2.1.6 - June 27, 2017 =
* Remove the header and footer in the blank page template
* Fix the layout of the edit address page of woocommerce
* Fix the submenu
* Remove the page title in the Blank  without container template
* Add hover effect on the sidebar menus
* Change the padding of the menu item
* Add hover effect in the menu item
* Change the font size of the site name

= 2.0.8 - June 27, 2017 =
* Add blank page width container and Blank page without container Template

= 2.0.7 - June 26, 2017 =
* Make the comments visible in page
* Add css style in the pingback and Trackback
* Fix the style of the Threaded comment list
* Change the password protected form with bootstrap 4 form
* Add style in the single post navigation
* Reduce the spacing between the page banner and the main content
* Change the footer link
* Remove the css that override Bootstrap 4 font
* Fix the price filter

= 2.0.6 - June 26, 2017 =
* Add form-control class in contact form 7 field

= 2.0.5 - June 23, 2017 =
* Fix the css to make the theme compatible in Visual Composer
* Adjust the code of content-page.php to remove the title if the page is build using visual composer

= 2.0.4 - June 23, 2017 =
* Fix the css to make the theme compatible in Elementor Page Builder just use the fullwidth template to include the header and footer in the page

= 2.0.3 - June 23, 2017 =
* Create woocommerce template
* Fix the sidebar in woocommerce page
* Fix the form in checkout page
* Fix the layout of the cart page
* Fix the lightbox and product gallery slider in single product page
* Fix the layout of 404 page

= 2.0.2 - June 21, 2017 =
* Add fallback menu

= 2.0.1 - June 15, 2017 =
* Adjust the breakpoint of the maincontent and sidebar
* Remove the jumbotron in the header of internal pages except the homepage
* Add spacing between the menu and the main content

= 2.0.0 - June 14, 2017 =
* Upgrade the theme to bootstrap 4
* Update the html markup to bootstrap 4
* Change the navwalker to work in bootstrap 4
* Fix the style of the header
* Fix the responsiveness of the menu

= 1.3.2 - June 8, 2017 =
* Added an option to add Logo

= 1.3.1 - June 6, 2017 =
* Added left sidebar template
* Added blank template
* Added fullwidth template

= 1.3.0 - June 5, 2017 =
* Added 3 footer widget area
* Added the function add_theme_support( "custom-header", $args )
* Added the function add_editor_style()

= 1.2.9 - May 26, 2017 =
* Added "link_before" and "link_after" attribute in the menu

= 1.2.8 - May 19, 2017 =
* Add theme description

= 1.2.7 - May 10, 2017 =
* Remove the css customization to make the theme simple

= 1.2.6 - May 4, 2017 =
* Include the theme URI in style.css

= 1.2.5 - May 2, 2017 =
* Remove the theme URI in style.css
* Fix the translatable content issue in template-tags.php
* Remove script in header.php and enqueue it in the functions.php
* Fix the Missing singular placeholder in comments.php
* Remove rtl.css
* Remove Languages folder in the theme
* Remove search-form from add_theme_support( 'html5' )
* Escape value of height and width with esc_attr()
* Add blog in theme tag
* Put back the colors and Background image section in Customizer
* Update the readme.txt

= 1.2.4 - March 22, 2017 =
* Remove the custom-editor-style.css
* Add space between Edit link and tags
* Remove custom-header file in the theme

= 1.2.3 - March 15, 2017 =
* Remove the layout directory containing the content-sidebar.css and sidebar-content.css
* Style the threaded comment

= 1.2.2 - March 14, 2017 =
* Add Fullwidth Template

= 1.2.1 - March 9, 2017 =
* Translate Toggle Navigation in header.php
* Add escape url for bloginfo('name');
* Remove menu from the wp_nav_menu();
* Fix the mobile navigation toggle button
* Adjust the spacing of the mobile navigation
* Adjust the spacing of the header
* Fix the broken sidebar in the search.php

= 1.2 - Nov 23, 2016 =
* Change the theme name to WP Bootstrap Starter

= 1.0 - Nov 13, 2016 =
* Initial release